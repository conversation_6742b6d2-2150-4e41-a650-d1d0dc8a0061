const http = require('http');
const WebSocket = require('ws');

async function controlYourChrome() {
  try {
    console.log('正在连接到您的Chrome浏览器...');
    
    // 获取Chrome调试信息
    const response = await fetch('http://localhost:9222/json');
    const tabs = await response.json();
    
    console.log('✅ 成功连接到Chrome！');
    console.log(`📄 发现 ${tabs.length} 个标签页`);
    
    // 查找百度页面或使用第一个页面
    let targetTab = tabs.find(tab => tab.url.includes('baidu.com')) || tabs[0];
    
    if (!targetTab) {
      console.log('❌ 没有找到可用的标签页');
      return;
    }
    
    console.log(`🎯 使用标签页: ${targetTab.title}`);
    console.log(`🔗 URL: ${targetTab.url}`);
    
    // 连接到WebSocket
    const ws = new WebSocket(targetTab.webSocketDebuggerUrl);
    
    ws.on('open', () => {
      console.log('🔌 WebSocket连接已建立');
      
      // 启用Runtime和Page域
      ws.send(JSON.stringify({id: 1, method: 'Runtime.enable'}));
      ws.send(JSON.stringify({id: 2, method: 'Page.enable'}));
      
      // 如果不在百度页面，先导航到百度
      if (!targetTab.url.includes('baidu.com')) {
        console.log('📍 导航到百度...');
        ws.send(JSON.stringify({
          id: 3, 
          method: 'Page.navigate', 
          params: {url: 'https://www.baidu.com'}
        }));
        
        // 等待页面加载后再搜索
        setTimeout(() => searchSanxia(ws), 3000);
      } else {
        // 直接搜索
        searchSanxia(ws);
      }
    });
    
    ws.on('message', (data) => {
      const message = JSON.parse(data);
      if (message.method === 'Page.loadEventFired') {
        console.log('📄 页面加载完成');
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket错误:', error);
    });
    
  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    console.log('\n💡 请确保：');
    console.log('1. Chrome浏览器已启动');
    console.log('2. 启动时包含 --remote-debugging-port=9222 参数');
  }
}

function searchSanxia(ws) {
  console.log('🔍 开始搜索"三峡大学"...');
  
  // 执行JavaScript代码来搜索
  const searchScript = `
    (function() {
      try {
        // 查找搜索框
        const searchBox = document.querySelector('#kw');
        if (searchBox) {
          searchBox.value = '三峡大学';
          searchBox.focus();
          
          // 查找搜索按钮并点击
          const searchBtn = document.querySelector('#su');
          if (searchBtn) {
            searchBtn.click();
            return '✅ 搜索已执行';
          } else {
            return '❌ 未找到搜索按钮';
          }
        } else {
          return '❌ 未找到搜索框';
        }
      } catch (error) {
        return '❌ 执行错误: ' + error.message;
      }
    })()
  `;
  
  ws.send(JSON.stringify({
    id: 10,
    method: 'Runtime.evaluate',
    params: {
      expression: searchScript,
      returnByValue: true
    }
  }));
  
  // 监听执行结果
  ws.on('message', (data) => {
    const message = JSON.parse(data);
    if (message.id === 10 && message.result) {
      console.log('📋 执行结果:', message.result.value);
      
      // 等待搜索结果加载后获取结果
      setTimeout(() => getSearchResults(ws), 2000);
    }
  });
}

function getSearchResults(ws) {
  console.log('📊 获取搜索结果...');
  
  const getResultsScript = `
    (function() {
      try {
        const results = [];
        const resultElements = document.querySelectorAll('.result h3 a');
        
        for (let i = 0; i < Math.min(5, resultElements.length); i++) {
          const link = resultElements[i];
          results.push({
            index: i + 1,
            title: link.textContent.trim(),
            url: link.href
          });
        }
        
        return results;
      } catch (error) {
        return '获取结果失败: ' + error.message;
      }
    })()
  `;
  
  ws.send(JSON.stringify({
    id: 11,
    method: 'Runtime.evaluate',
    params: {
      expression: getResultsScript,
      returnByValue: true
    }
  }));
  
  ws.on('message', (data) => {
    const message = JSON.parse(data);
    if (message.id === 11 && message.result) {
      const results = message.result.value;
      
      if (Array.isArray(results)) {
        console.log('\n🎯 三峡大学搜索结果：');
        results.forEach(result => {
          console.log(`${result.index}. ${result.title}`);
          console.log(`   ${result.url}\n`);
        });
      } else {
        console.log('📋 结果:', results);
      }
      
      console.log('✅ 自动化搜索完成！');
      console.log('🔧 您的Chrome浏览器现在显示三峡大学的搜索结果');
    }
  });
}

// 运行控制脚本
controlYourChrome();
