const puppeteer = require('puppeteer-core');

async function controlYourChrome() {
  try {
    console.log('正在连接到您的Chrome浏览器...');
    
    // 连接到已经启动的Chrome实例（通过调试端口9222）
    const browser = await puppeteer.connect({
      browserURL: 'http://localhost:9222',
      defaultViewport: null
    });

    console.log('✅ 成功连接到您的Chrome浏览器！');

    // 获取所有页面
    const pages = await browser.pages();
    let page;
    
    // 查找百度页面，如果没有则创建新页面
    const baiduPage = pages.find(p => p.url().includes('baidu.com'));
    if (baiduPage) {
      page = baiduPage;
      console.log('📄 找到百度页面，使用现有页面');
    } else {
      page = await browser.newPage();
      console.log('📄 创建新页面');
      await page.goto('https://www.baidu.com');
    }

    // 确保页面处于活动状态
    await page.bringToFront();

    console.log('🔍 开始自动搜索"三峡大学"...');

    // 等待搜索框加载
    await page.waitForSelector('#kw', { timeout: 10000 });

    // 清空搜索框并输入"三峡大学"
    await page.click('#kw');
    await page.keyboard.selectAll();
    await page.type('#kw', '三峡大学', { delay: 100 });

    console.log('✏️ 已输入搜索关键词：三峡大学');

    // 点击搜索按钮
    await page.click('#su');
    console.log('🔍 已点击搜索按钮');

    // 等待搜索结果加载
    await page.waitForSelector('.result', { timeout: 15000 });
    console.log('📋 搜索结果已加载');

    // 获取搜索结果
    const results = await page.evaluate(() => {
      const resultElements = document.querySelectorAll('.result h3 a');
      return Array.from(resultElements).slice(0, 5).map((link, index) => ({
        index: index + 1,
        title: link.textContent.trim(),
        url: link.href
      }));
    });

    console.log('\n🎯 搜索结果：');
    results.forEach(result => {
      console.log(`${result.index}. ${result.title}`);
      console.log(`   ${result.url}\n`);
    });

    // 截取搜索结果页面
    const screenshotPath = 'C:\\Users\\<USER>\\Downloads\\your-chrome-search-results.png';
    await page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });
    console.log(`📸 截图已保存到：${screenshotPath}`);

    console.log('\n✅ 自动化操作完成！');
    console.log('🔧 您的Chrome浏览器现在显示三峡大学的搜索结果');
    console.log('💾 所有操作都使用了您的Chrome用户数据（密码、登录状态等）');

    // 不关闭浏览器，让您继续使用
    console.log('\n📝 浏览器将保持打开状态，您可以继续手动操作');

  } catch (error) {
    console.error('❌ 操作失败:', error.message);
    
    if (error.message.includes('connect')) {
      console.log('\n💡 解决方案：');
      console.log('1. 确保Chrome浏览器已启动并开启调试端口');
      console.log('2. 重新运行启动Chrome的命令');
      console.log('3. 检查端口9222是否被占用');
    }
  }
}

// 运行自动化脚本
controlYourChrome();
