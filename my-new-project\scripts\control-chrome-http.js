const http = require('http');

async function controlYourChrome() {
  try {
    console.log('正在连接到您的Chrome浏览器...');
    
    // 使用http模块获取Chrome调试信息
    const options = {
      hostname: 'localhost',
      port: 9222,
      path: '/json',
      method: 'GET'
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const tabs = JSON.parse(data);
          console.log('✅ 成功连接到Chrome！');
          console.log(`📄 发现 ${tabs.length} 个标签页`);
          
          // 查找百度页面或使用第一个页面
          let targetTab = tabs.find(tab => tab.url.includes('baidu.com')) || tabs[0];
          
          if (!targetTab) {
            console.log('❌ 没有找到可用的标签页');
            return;
          }
          
          console.log(`🎯 使用标签页: ${targetTab.title}`);
          console.log(`🔗 URL: ${targetTab.url}`);
          
          // 执行搜索
          executeSearch(targetTab.id);
          
        } catch (error) {
          console.error('❌ 解析响应失败:', error.message);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ 连接失败:', error.message);
      console.log('\n💡 请确保：');
      console.log('1. Chrome浏览器已启动');
      console.log('2. 启动时包含 --remote-debugging-port=9222 参数');
      console.log('3. 端口9222未被其他程序占用');
    });
    
    req.end();
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  }
}

function executeSearch(tabId) {
  console.log('🔍 开始在您的Chrome中搜索"三峡大学"...');
  
  // 构建搜索脚本
  const searchScript = `
    (function() {
      try {
        // 如果不在百度页面，先导航到百度
        if (!window.location.href.includes('baidu.com')) {
          window.location.href = 'https://www.baidu.com';
          return '正在导航到百度...';
        }
        
        // 查找搜索框
        const searchBox = document.querySelector('#kw');
        if (searchBox) {
          searchBox.value = '三峡大学';
          searchBox.focus();
          
          // 查找搜索按钮并点击
          const searchBtn = document.querySelector('#su');
          if (searchBtn) {
            searchBtn.click();
            
            // 等待一下然后获取结果
            setTimeout(function() {
              const results = [];
              const resultElements = document.querySelectorAll('.result h3 a');
              
              for (let i = 0; i < Math.min(5, resultElements.length); i++) {
                const link = resultElements[i];
                results.push({
                  index: i + 1,
                  title: link.textContent.trim(),
                  url: link.href
                });
              }
              
              console.log('🎯 三峡大学搜索结果：');
              results.forEach(result => {
                console.log(result.index + '. ' + result.title);
                console.log('   ' + result.url);
              });
              
            }, 2000);
            
            return '✅ 搜索已执行，正在获取结果...';
          } else {
            return '❌ 未找到搜索按钮';
          }
        } else {
          return '❌ 未找到搜索框，可能需要先导航到百度';
        }
      } catch (error) {
        return '❌ 执行错误: ' + error.message;
      }
    })()
  `;
  
  // 发送执行命令
  const postData = JSON.stringify({
    expression: searchScript,
    returnByValue: true
  });
  
  const options = {
    hostname: 'localhost',
    port: 9222,
    path: `/json/runtime/evaluate`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };
  
  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('📋 执行结果:', result);
        console.log('✅ 自动化搜索完成！');
        console.log('🔧 您的Chrome浏览器现在应该显示三峡大学的搜索结果');
      } catch (error) {
        console.log('📋 命令已发送到Chrome');
        console.log('✅ 请查看您的Chrome浏览器窗口');
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ 发送命令失败:', error.message);
  });
  
  req.write(postData);
  req.end();
}

// 运行控制脚本
controlYourChrome();
