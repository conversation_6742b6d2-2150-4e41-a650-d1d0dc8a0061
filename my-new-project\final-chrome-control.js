// 最终版本：控制您的Chrome浏览器
const http = require('http');

console.log('🎯 正在连接到您的Chrome浏览器进行自动化控制...');

// 主要控制函数
async function controlYourChrome() {
  try {
    // 1. 首先导航到百度
    console.log('📍 导航到百度...');
    await navigateTo('https://www.baidu.com');
    
    // 2. 等待页面加载
    await sleep(2000);
    
    // 3. 执行搜索
    console.log('🔍 搜索"三峡大学"...');
    await executeScript(`
      // 填写搜索框
      const searchBox = document.querySelector('#kw');
      if (searchBox) {
        searchBox.value = '三峡大学';
        searchBox.focus();
      }
      
      // 点击搜索按钮
      const searchBtn = document.querySelector('#su');
      if (searchBtn) {
        searchBtn.click();
      }
    `);
    
    // 4. 等待搜索结果
    await sleep(3000);
    
    // 5. 获取搜索结果
    console.log('📊 获取搜索结果...');
    const results = await executeScript(`
      const results = [];
      const resultElements = document.querySelectorAll('.result h3 a');
      
      for (let i = 0; i < Math.min(5, resultElements.length); i++) {
        const link = resultElements[i];
        results.push({
          index: i + 1,
          title: link.textContent.trim(),
          url: link.href
        });
      }
      
      return results;
    `);
    
    // 6. 显示结果
    if (results && Array.isArray(results)) {
      console.log('\n🎯 三峡大学搜索结果：');
      results.forEach(result => {
        console.log(`${result.index}. ${result.title}`);
        console.log(`   ${result.url}\n`);
      });
    }
    
    console.log('✅ 自动化控制完成！');
    console.log('🔧 您的Chrome浏览器现在显示三峡大学的搜索结果');
    
  } catch (error) {
    console.error('❌ 控制失败:', error.message);
  }
}

// 导航到指定URL
function navigateTo(url) {
  return new Promise((resolve, reject) => {
    const script = `window.location.href = '${url}';`;
    executeScript(script).then(resolve).catch(reject);
  });
}

// 执行JavaScript脚本
function executeScript(script) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      expression: `(function() { ${script} })()`,
      returnByValue: true
    });
    
    const options = {
      hostname: 'localhost',
      port: 9222,
      path: '/json/runtime/evaluate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result.result?.value);
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

// 等待函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 检查连接并开始控制
function checkConnectionAndStart() {
  const req = http.get('http://localhost:9222/json', (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const tabs = JSON.parse(data);
        console.log(`✅ 成功连接到您的Chrome！发现 ${tabs.length} 个标签页`);
        
        // 开始控制
        controlYourChrome();
        
      } catch (error) {
        console.error('❌ 解析响应失败:', error.message);
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ 连接失败:', error.message);
    console.log('\n💡 请确保：');
    console.log('1. 您的Chrome浏览器已启动');
    console.log('2. Chrome启动时包含 --remote-debugging-port=9222 参数');
    console.log('3. 可以手动运行: start-your-chrome.bat');
  });
}

// 开始执行
checkConnectionAndStart();
