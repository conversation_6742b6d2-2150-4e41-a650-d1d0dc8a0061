const http = require('http');

console.log('开始测试Chrome调试连接...');

const req = http.get('http://localhost:9222/json', (res) => {
  console.log('状态码:', res.statusCode);
  console.log('响应头:', res.headers);
  
  let data = '';
  res.on('data', chunk => {
    data += chunk;
    console.log('接收数据:', chunk.toString());
  });
  
  res.on('end', () => {
    console.log('完整响应:', data);
    try {
      const tabs = JSON.parse(data);
      console.log('解析成功，标签页数量:', tabs.length);
      tabs.forEach((tab, i) => {
        console.log(`${i+1}. ${tab.title} - ${tab.url}`);
      });
    } catch (error) {
      console.log('解析失败:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.log('请求错误:', error.message);
  console.log('错误代码:', error.code);
});

req.setTimeout(5000, () => {
  console.log('请求超时');
  req.destroy();
});

console.log('请求已发送...');
