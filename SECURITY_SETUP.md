# 🔐 安全配置指南

本文档包含需要手动配置的安全设置，以确保项目的安全性。

## ✅ 已自动修复的安全问题

- [x] 环境变量泄露问题已修复
- [x] RLS策略性能问题已优化
- [x] 数据库函数安全问题已修复

## 🔧 需要手动配置的安全设置

### 1. 启用密码泄露保护

**位置**: Supabase Dashboard → Authentication → Settings → Password Security

**配置步骤**:
1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择您的项目
3. 导航到 Authentication → Settings
4. 找到 "Password Security" 部分
5. 启用 "Leaked Password Protection"
   - 这将使用 HaveIBeenPwned.org API 检查密码是否已泄露

### 2. 调整OTP过期时间

**位置**: Supabase Dashboard → Authentication → Settings → Email/SMS

**配置步骤**:
1. 在 Authentication → Settings 页面
2. 找到 "Email" 或 "SMS" 配置部分
3. 将 OTP 过期时间设置为 **1小时以内**（推荐30分钟）

### 3. 加强密码强度要求

**位置**: Supabase Dashboard → Authentication → Settings → Password Security

**推荐配置**:
- 最小密码长度: **8个字符**（当前可能更低）
- 要求字符类型: **数字、大小写字母、特殊符号**
- 启用密码泄露检查: **是**

### 4. 配置会话安全

**位置**: Supabase Dashboard → Authentication → Settings → Sessions

**推荐配置**:
- JWT 过期时间: **1小时**（默认值，不建议更长）
- 会话超时: 根据业务需求设置
- 单用户单会话: 根据安全需求考虑启用

## 🚨 紧急安全提醒

### 立即执行的操作

1. **重新生成API密钥**（如果担心泄露）:
   ```bash
   # 在Supabase Dashboard中重新生成
   # 然后更新 .env.local 文件
   ```

2. **检查访问日志**:
   - 查看是否有异常访问
   - 监控数据库连接

3. **更新所有环境**:
   - 确保生产环境使用新的环境变量
   - 更新CI/CD配置

## 📋 安全检查清单

- [ ] 密码泄露保护已启用
- [ ] OTP过期时间已调整（≤1小时）
- [ ] 密码强度要求已加强
- [ ] 环境变量已从版本控制中移除
- [ ] .env.local 文件已重新创建（本地）
- [ ] 生产环境变量已更新
- [ ] 数据库函数安全问题已修复
- [ ] RLS策略性能已优化

## 🔍 验证安全修复

运行以下命令验证修复效果：

```bash
# 检查环境变量是否已从Git中移除
git status

# 验证.env.local不在版本控制中
git check-ignore .env.local
```

## 📞 如需帮助

如果在配置过程中遇到问题，请：
1. 查看 [Supabase 安全文档](https://supabase.com/docs/guides/platform/going-into-prod#security)
2. 检查 Supabase Dashboard 中的安全建议
3. 联系技术支持

---

**重要**: 完成所有配置后，请删除此文件或将其移至安全位置，避免暴露安全配置信息。
