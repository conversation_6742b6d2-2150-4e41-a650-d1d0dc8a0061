// Chrome浏览器配置
const chromeConfig = {
  // Chrome可执行文件路径
  executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
  
  // 用户数据目录（包含保存的密码、cookies等）
  userDataDir: 'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data',
  
  // 启动参数
  args: [
    '--start-maximized',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor',
    '--remote-debugging-port=9222' // 允许远程调试
  ],
  
  // 浏览器选项
  options: {
    headless: false,
    viewport: { width: 1280, height: 720 },
    slowMo: 100 // 操作间隔，便于观察
  }
};

module.exports = chromeConfig;
