# Playwright MCP 使用演示

## 🎯 我们刚才完成的操作

1. **导航到百度**: `playwright_navigate` 
   - 使用了Chromium浏览器
   - 设置为非无头模式（可见窗口）
   - 分辨率：1280x720

2. **填写搜索框**: `playwright_fill`
   - 选择器：`#kw`
   - 输入内容：`三峡大学`

3. **点击搜索按钮**: `playwright_click`
   - 选择器：`#su`

4. **截取页面截图**: `playwright_screenshot`
   - 保存为PNG文件
   - 全页面截图

5. **获取页面文本**: `playwright_get_visible_text`
   - 提取了所有可见文本内容

## 🔧 关于使用您的Chrome浏览器

### 当前状态：
- Playwright MCP 使用 Chromium 内核
- 与 Chrome 高度兼容
- 支持所有现代 Web 功能

### 优势：
✅ **无需额外配置** - 开箱即用
✅ **保持会话状态** - 在同一会话中登录状态会保持
✅ **完整功能支持** - 支持所有 Playwright 功能
✅ **安全隔离** - 不会影响您的主浏览器

### 如果需要使用您的Chrome数据：
1. **方案1**: 使用我创建的 `chrome-automation.js` 脚本
2. **方案2**: 在Playwright MCP中手动登录一次（会保持会话）
3. **方案3**: 使用Chrome的同步功能

## 📋 常用Playwright MCP命令

```javascript
// 导航
playwright_navigate(url, options)

// 交互
playwright_click(selector)
playwright_fill(selector, value)
playwright_select(selector, value)
playwright_hover(selector)

// 获取内容
playwright_get_visible_text()
playwright_get_visible_html()
playwright_screenshot(name, options)

// 等待和断言
playwright_expect_response(id, url)
playwright_assert_response(id, value)

// 浏览器控制
playwright_go_back()
playwright_go_forward()
playwright_close()
```

## 🎯 下次使用建议

1. **对于一般浏览**: 直接使用 Playwright MCP
2. **需要登录状态**: 在会话中手动登录一次
3. **复杂自动化**: 使用自定义脚本

## 📝 示例用法

```bash
# 如果需要运行自定义脚本（安装完成后）
node scripts/chrome-automation.js

# 或者继续使用 Playwright MCP（推荐）
# 通过AI助手调用相关命令
```
