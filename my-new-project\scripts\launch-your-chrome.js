const { spawn } = require('child_process');
const path = require('path');

// 您指定的Chrome路径
const chromePath = 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe';

// Chrome启动参数
const chromeArgs = [
  '--remote-debugging-port=9222',  // 启用远程调试
  '--user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data', // 使用您的用户数据
  '--start-maximized',             // 最大化窗口
  '--disable-web-security',        // 禁用web安全（用于自动化）
  '--disable-features=VizDisplayCompositor',
  'https://www.baidu.com'          // 直接打开百度
];

console.log('正在启动您的Chrome浏览器...');
console.log('Chrome路径:', chromePath);
console.log('启动参数:', chromeArgs.join(' '));

// 启动Chrome
const chrome = spawn(chromePath, chromeArgs, {
  detached: true,
  stdio: 'ignore'
});

chrome.unref(); // 让Chrome独立运行

console.log('✅ Chrome已启动！');
console.log('🔧 特性：');
console.log('  - 使用您的用户数据（保存的密码、书签等）');
console.log('  - 启用远程调试端口9222');
console.log('  - 直接打开百度页面');
console.log('  - 可以手动操作，也可以通过代码控制');

console.log('\n📝 接下来您可以：');
console.log('1. 在打开的Chrome中手动搜索"三峡大学"');
console.log('2. 使用您保存的账号密码登录各种网站');
console.log('3. 正常浏览，所有数据都会保存到您的Chrome配置中');

// 如果需要通过代码控制，可以连接到调试端口
console.log('\n🔗 如果需要代码控制：');
console.log('可以通过 http://localhost:9222 连接到Chrome进行自动化操作');
