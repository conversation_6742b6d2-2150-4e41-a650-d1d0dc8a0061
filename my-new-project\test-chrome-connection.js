// 简单测试Chrome连接
const http = require('http');

console.log('测试连接到您的Chrome浏览器...');

const options = {
  hostname: 'localhost',
  port: 9222,
  path: '/json',
  method: 'GET'
};

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const tabs = JSON.parse(data);
      console.log('✅ 成功连接到Chrome！');
      console.log(`📄 发现 ${tabs.length} 个标签页：`);
      
      tabs.forEach((tab, index) => {
        console.log(`${index + 1}. ${tab.title}`);
        console.log(`   URL: ${tab.url}`);
        console.log(`   ID: ${tab.id}`);
        console.log('');
      });
      
      // 查找百度页面
      const baiduTab = tabs.find(tab => tab.url.includes('baidu.com'));
      if (baiduTab) {
        console.log('🎯 找到百度页面！');
        console.log('现在可以控制这个页面进行搜索');
      } else {
        console.log('📝 没有找到百度页面，但可以在任意页面中导航到百度');
      }
      
    } catch (error) {
      console.error('❌ 解析响应失败:', error.message);
      console.log('原始响应:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ 连接失败:', error.message);
  console.log('\n💡 可能的原因：');
  console.log('1. Chrome浏览器未启动');
  console.log('2. Chrome启动时没有包含 --remote-debugging-port=9222 参数');
  console.log('3. 端口9222被其他程序占用');
  console.log('4. 防火墙阻止了连接');
});

req.end();
