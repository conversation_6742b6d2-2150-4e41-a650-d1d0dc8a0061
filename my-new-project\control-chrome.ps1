# PowerShell脚本控制您的Chrome浏览器
Write-Host "正在连接到您的Chrome浏览器..." -ForegroundColor Green

try {
    # 获取Chrome标签页信息
    $response = Invoke-RestMethod -Uri "http://localhost:9222/json" -Method Get
    
    Write-Host "✅ 成功连接到Chrome！" -ForegroundColor Green
    Write-Host "📄 发现 $($response.Count) 个标签页" -ForegroundColor Cyan
    
    # 查找百度页面或使用第一个页面
    $targetTab = $response | Where-Object { $_.url -like "*baidu.com*" } | Select-Object -First 1
    if (-not $targetTab) {
        $targetTab = $response[0]
    }
    
    if (-not $targetTab) {
        Write-Host "❌ 没有找到可用的标签页" -ForegroundColor Red
        exit
    }
    
    Write-Host "🎯 使用标签页: $($targetTab.title)" -ForegroundColor Yellow
    Write-Host "🔗 URL: $($targetTab.url)" -ForegroundColor Yellow
    
    # 构建搜索脚本
    $searchScript = @"
(function() {
    try {
        // 如果不在百度页面，先导航到百度
        if (!window.location.href.includes('baidu.com')) {
            window.location.href = 'https://www.baidu.com';
            return '正在导航到百度...';
        }
        
        // 查找搜索框
        const searchBox = document.querySelector('#kw');
        if (searchBox) {
            searchBox.value = '三峡大学';
            searchBox.focus();
            
            // 查找搜索按钮并点击
            const searchBtn = document.querySelector('#su');
            if (searchBtn) {
                searchBtn.click();
                return '✅ 搜索已执行：三峡大学';
            } else {
                return '❌ 未找到搜索按钮';
            }
        } else {
            return '❌ 未找到搜索框，可能需要先导航到百度';
        }
    } catch (error) {
        return '❌ 执行错误: ' + error.message;
    }
})()
"@
    
    # 准备POST数据
    $postData = @{
        expression = $searchScript
        returnByValue = $true
    } | ConvertTo-Json
    
    Write-Host "🔍 开始在您的Chrome中搜索'三峡大学'..." -ForegroundColor Green
    
    # 发送执行命令到Chrome
    $executeUrl = "http://localhost:9222/json/runtime/evaluate"
    $headers = @{
        'Content-Type' = 'application/json'
    }
    
    $result = Invoke-RestMethod -Uri $executeUrl -Method Post -Body $postData -Headers $headers
    
    Write-Host "📋 执行结果: $($result.result.value)" -ForegroundColor Cyan
    Write-Host "✅ 自动化搜索完成！" -ForegroundColor Green
    Write-Host "🔧 请查看您的Chrome浏览器窗口，应该显示三峡大学的搜索结果" -ForegroundColor Yellow
    
    # 等待一下然后获取搜索结果
    Start-Sleep -Seconds 3
    
    $getResultsScript = @"
(function() {
    try {
        const results = [];
        const resultElements = document.querySelectorAll('.result h3 a');
        
        for (let i = 0; i < Math.min(5, resultElements.length); i++) {
            const link = resultElements[i];
            results.push({
                index: i + 1,
                title: link.textContent.trim(),
                url: link.href
            });
        }
        
        return results;
    } catch (error) {
        return '获取结果失败: ' + error.message;
    }
})()
"@
    
    $getResultsData = @{
        expression = $getResultsScript
        returnByValue = $true
    } | ConvertTo-Json
    
    Write-Host "📊 获取搜索结果..." -ForegroundColor Green
    $resultsResponse = Invoke-RestMethod -Uri $executeUrl -Method Post -Body $getResultsData -Headers $headers
    
    if ($resultsResponse.result.value -is [array]) {
        Write-Host "`n🎯 三峡大学搜索结果：" -ForegroundColor Green
        foreach ($result in $resultsResponse.result.value) {
            Write-Host "$($result.index). $($result.title)" -ForegroundColor White
            Write-Host "   $($result.url)`n" -ForegroundColor Gray
        }
    } else {
        Write-Host "📋 结果: $($resultsResponse.result.value)" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ 操作失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`n💡 请确保：" -ForegroundColor Yellow
    Write-Host "1. Chrome浏览器已启动" -ForegroundColor White
    Write-Host "2. 启动时包含 --remote-debugging-port=9222 参数" -ForegroundColor White
    Write-Host "3. 端口9222未被其他程序占用" -ForegroundColor White
}
