// 简单测试连接
const http = require('http');

setTimeout(() => {
  console.log('测试连接到localhost:9222...');
  
  const req = http.get('http://localhost:9222/json', (res) => {
    console.log('✅ 连接成功！状态码:', res.statusCode);
    
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const tabs = JSON.parse(data);
        console.log(`📄 发现 ${tabs.length} 个标签页`);
        
        // 显示前3个标签页
        tabs.slice(0, 3).forEach((tab, i) => {
          console.log(`${i+1}. ${tab.title}`);
          console.log(`   ${tab.url}`);
        });
        
        // 查找百度页面
        const baiduTab = tabs.find(tab => tab.url.includes('baidu.com'));
        if (baiduTab) {
          console.log('\n🎯 找到百度页面！');
          console.log('现在可以控制这个页面');
          
          // 执行搜索
          executeSearch();
        } else {
          console.log('\n📝 没有找到百度页面');
        }
        
      } catch (error) {
        console.log('❌ 解析失败:', error.message);
        console.log('原始数据:', data);
      }
    });
  });
  
  req.on('error', (error) => {
    console.log('❌ 连接失败:', error.message);
    console.log('请确保Chrome已启动并开启调试端口9222');
  });
  
}, 3000); // 等待3秒

function executeSearch() {
  console.log('🔍 执行搜索...');
  
  const searchScript = `
    (function() {
      try {
        const searchBox = document.querySelector('#kw');
        if (searchBox) {
          searchBox.value = '三峡大学';
          const searchBtn = document.querySelector('#su');
          if (searchBtn) {
            searchBtn.click();
            return '搜索已执行';
          }
        }
        return '未找到搜索元素';
      } catch (error) {
        return '错误: ' + error.message;
      }
    })()
  `;
  
  const postData = JSON.stringify({
    expression: searchScript,
    returnByValue: true
  });
  
  const options = {
    hostname: 'localhost',
    port: 9222,
    path: '/json/runtime/evaluate',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };
  
  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log('📋 搜索结果:', data);
      console.log('✅ 操作完成！请查看您的Chrome窗口');
    });
  });
  
  req.on('error', (error) => {
    console.log('❌ 搜索失败:', error.message);
  });
  
  req.write(postData);
  req.end();
}
